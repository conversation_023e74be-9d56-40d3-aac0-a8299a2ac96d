'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertCircle, Globe, Plus, Settings } from 'lucide-react'
import { useState } from 'react'
import { DomainForm } from './components/domain-form'

interface Domain {
  id: string
  hostname: string
  name: string
  description?: string
  domain_type: 'subdomain' | 'custom'
  status: 'pending' | 'active' | 'failed' | 'suspended'
  is_active: boolean
  created_at: string
}

export default function DomainsPage() {
  const { user } = useAuth()
  const [showForm, setShowForm] = useState(false)
  const [selectedDomain, setSelectedDomain] = useState<Domain | null>(null)
  const queryClient = useQueryClient()

  // Use React Query to fetch domain (single domain per organization)
  const {
    data: domain = null,
    isLoading,
    error,
  } = useQuery<Domain | null>({
    queryKey: ['domains', user?.current_organization?.id],
    queryFn: async (): Promise<Domain | null> => {
      // Get current organization ID from user
      const orgId = user?.current_organization?.id

      if (!orgId) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }

      const response = await queryFetchHelper(`/organizations/${orgId}/domains`)
      return response.data || null
    },
    enabled: !!user?.current_organization?.id, // Only fetch when orgId is available
  })

  const handleAddDomain = () => {
    setSelectedDomain(null)
    setShowForm(true)
  }

  const handleEditDomain = (domain: Domain) => {
    setSelectedDomain(domain)
    setShowForm(true)
  }

  const getStatusBadge = (domain: Domain) => {
    switch (domain.status) {
      case 'active':
        return (
          <Badge
            variant="default"
            className="bg-green-500">
            Hoạt động
          </Badge>
        )
      case 'pending':
        return (
          <Badge
            variant="outline"
            className="text-blue-600">
            Đang chờ
          </Badge>
        )
      case 'failed':
        return <Badge variant="destructive">Lỗi</Badge>
      case 'suspended':
        return (
          <Badge
            variant="outline"
            className="text-red-600">
            Tạm dừng
          </Badge>
        )
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
          <h3 className="mb-2 text-lg font-semibold">Có lỗi xảy ra</h3>
          <p className="text-muted-foreground">
            {error instanceof Error ? error.message : 'Không thể tải danh sách domain'}
          </p>
        </div>
      </div>
    )
  }

  if (!user?.current_organization?.id) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-yellow-500" />
          <h3 className="mb-2 text-lg font-semibold">Không tìm thấy tổ chức</h3>
          <p className="text-muted-foreground">Vui lòng đăng nhập lại hoặc chọn tổ chức</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Quản lý Domain</h1>
          <p className="text-muted-foreground">
            Cấu hình domain cho tổ chức của bạn. Mỗi tổ chức chỉ được phép có một domain duy nhất.
          </p>
        </div>
        {!domain && (
          <Button onClick={handleAddDomain}>
            <Plus className="mr-2 h-4 w-4" />
            Thêm Domain
          </Button>
        )}
      </div>

      <div className="grid gap-4">
        {!domain ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Globe className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">Chưa có domain nào</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Cấu hình domain để sử dụng white-label cho tổ chức của bạn.
                <br />
                Mỗi tổ chức chỉ được phép có một domain duy nhất.
              </p>
              <Button onClick={handleAddDomain}>
                <Plus className="mr-2 h-4 w-4" />
                Thêm Domain
              </Button>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Globe className="h-5 w-5" />
                  <div>
                    <CardTitle>{domain.hostname}</CardTitle>
                    <CardDescription>{domain.name}</CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(domain)}
                  <Badge variant="outline">{domain.domain_type === 'custom' ? 'Custom' : 'Subdomain'}</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-muted-foreground flex items-center gap-4 text-sm">
                  <span>Tạo: {new Date(domain.created_at).toLocaleDateString('vi-VN')}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditDomain(domain)}>
                    <Settings className="mr-2 h-4 w-4" />
                    Chỉnh sửa
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {showForm && (
        <DomainForm
          domain={selectedDomain}
          onClose={() => {
            setShowForm(false)
            setSelectedDomain(null)
            queryClient.invalidateQueries({ queryKey: ['domains'] })
          }}
        />
      )}
    </div>
  )
}
