import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import globalConfig from './lib/config'

export async function middleware(request: NextRequest): Promise<NextResponse> {
  const { cookies, nextUrl } = request
  const pathname = nextUrl.pathname
  const hostname = request.headers.get('host') || request.nextUrl.hostname
  const isLoggedIn = cookies.has(process.env.NEXT_PUBLIC_APP_NAME + '.authorization')

  // Get domain configuration from backend
  let domainConfig = null
  try {
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/domains/config?hostname=${hostname}`, {
      headers: {
        Accept: 'application/json',
      },
    })

    if (configResponse.ok) {
      const configData = await configResponse.json()
      if (configData.success) {
        domainConfig = configData.data
      }
    }
  } catch (error) {
    console.warn('Failed to fetch domain config:', error)
  }

  // Use domain-specific configuration if available, otherwise fallback to global config
  const publicPaths = domainConfig?.custom?.publicPaths || globalConfig.publicPaths
  const authPaths = domainConfig?.custom?.authPaths || globalConfig.authPaths

  /**
   * Check if a path matches exactly or is a sub-path
   */
  const isPathMatch = (paths: string[], currentPath: string): boolean => {
    return paths.some((path) => currentPath === path || currentPath.startsWith(`${path}/`))
  }

  const isPublicPage = isPathMatch(publicPaths, pathname)
  const isAuthPage = isPathMatch(authPaths, pathname)

  // Create response
  let response = NextResponse.next()

  // Handle domain-specific redirects
  if (domainConfig?.custom?.redirect_to) {
    return NextResponse.redirect(domainConfig.custom.redirect_to)
  }

  // Force HTTPS if configured
  if (
    domainConfig?.custom?.force_https !== false &&
    !request.nextUrl.protocol.includes('https') &&
    process.env.NODE_ENV === 'production'
  ) {
    return NextResponse.redirect(`https://${hostname}${pathname}`)
  }

  // Redirect logic
  if (isLoggedIn) {
    // If a user is logged in and tries to access auth pages, redirect to home
    if (isAuthPage) {
      response = NextResponse.redirect(new URL('/', request.url))
    }
  } else if (!isPublicPage && !isAuthPage) {
    // If a user is not logged in and tries to access non-public, non-auth pages, redirect to log in
    response = NextResponse.redirect(new URL('/login', request.url))
  }

  // Add domain configuration to headers for use in components
  if (domainConfig) {
    response.headers.set('x-domain-config', JSON.stringify(domainConfig))
  }

  // Add domain-specific headers
  if (domainConfig?.custom?.headers) {
    Object.entries(domainConfig.custom.headers).forEach(([key, value]) => {
      response.headers.set(key, value as string)
    })
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
  ],
}
