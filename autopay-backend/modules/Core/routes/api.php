<?php

use Illuminate\Support\Facades\Route;
use Modules\Core\Http\Controllers\DomainController;

// Public domain configuration endpoint
Route::get('/domains/config', [DomainController::class, 'getConfig']);

// Organization-level domain management routes
Route::group([
    'prefix' => '{organization}',
    'middleware' => 'organization.standard',
], static function () {
    Route::apiResource('domains', DomainController::class)
        ->middleware('organization.permission:domain:instance:manage');
    Route::post('domains/{id}/set-default', [DomainController::class, 'setDefault'])
        ->middleware('organization.permission:domain:instance:manage');

    Route::post('domains/setup', [DomainController::class, 'setupDomain'])
        ->middleware('organization.permission:domain:instance:create');
});

Route::group([
    'prefix' => '{organization}/{team}',
    'middleware' => 'team.standard',
], function () {
    //
});
